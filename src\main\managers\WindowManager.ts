/**
 * 視窗管理器
 * 負責創建和管理所有應用程式視窗
 */

import { BrowserWindow, app } from 'electron'
import path from 'path'
import { isDev, getDevServerUrl, getDistPath } from '../utils/environment'

export class WindowManager {
  private mainWindow: BrowserWindow | null = null
  private recordingWindow: BrowserWindow | null = null

  /**
   * 創建主視窗
   */
  createMainWindow(): BrowserWindow {
    console.log('Creating main window, isDev:', isDev)

    this.mainWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false, // 主視窗預設隱藏，只有點擊托盤才顯示
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      icon: isDev
        ? path.join(process.cwd(), 'assets/tray-icon.png')
        : path.join(__dirname, '../../../assets/tray-icon.png')
    })

    this.loadWindowContent(this.mainWindow)

    // 主視窗關閉時隱藏而不是退出
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault()
        this.mainWindow?.hide()
      }
    })

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    return this.mainWindow
  }

  /**
   * 創建錄音視窗
   */
  createRecordingWindow(): BrowserWindow {
    console.log('Creating recording window, isDev:', isDev)

    this.recordingWindow = new BrowserWindow({
      width: 400,
      height: 300,
      frame: false,
      alwaysOnTop: true,
      resizable: false,
      transparent: true,
      show: false, // 初始隱藏，等待內容載入完成
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    })

    this.loadWindowContent(this.recordingWindow)

    // 居中顯示
    this.recordingWindow.center()

    this.recordingWindow.on('closed', () => {
      this.recordingWindow = null
    })

    return this.recordingWindow
  }

  /**
   * 載入視窗內容
   */
  private loadWindowContent(window: BrowserWindow) {
    if (isDev) {
      console.log('Loading window from dev server')
      window.loadURL(getDevServerUrl())
      if (window === this.mainWindow) {
        window.webContents.openDevTools()
      }
    } else {
      console.log('Loading window from dist')
      window.loadFile(path.join(__dirname, getDistPath()))
    }
  }

  /**
   * 顯示主視窗
   */
  showMainWindow() {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      this.createMainWindow()
    }
    this.mainWindow?.show()
    this.mainWindow?.focus()
  }

  /**
   * 隱藏主視窗
   */
  hideMainWindow() {
    this.mainWindow?.hide()
  }

  /**
   * 顯示錄音視窗
   */
  showRecordingWindow(): BrowserWindow {
    if (!this.recordingWindow || this.recordingWindow.isDestroyed()) {
      this.createRecordingWindow()
    }

    if (!this.recordingWindow) {
      throw new Error('Failed to create recording window')
    }

    // 確保內容載入完成後再顯示
    if (this.recordingWindow.webContents.isLoading()) {
      this.recordingWindow.webContents.once('did-finish-load', () => {
        this.recordingWindow?.show()
        this.recordingWindow?.focus()
      })
    } else {
      this.recordingWindow.show()
      this.recordingWindow.focus()
    }

    return this.recordingWindow
  }

  /**
   * 關閉錄音視窗
   */
  closeRecordingWindow() {
    if (this.recordingWindow && !this.recordingWindow.isDestroyed()) {
      this.recordingWindow.close()
    }
  }

  /**
   * 獲取主視窗
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  /**
   * 獲取錄音視窗
   */
  getRecordingWindow(): BrowserWindow | null {
    return this.recordingWindow
  }

  /**
   * 檢查錄音視窗是否存在且有效
   */
  isRecordingWindowValid(): boolean {
    return this.recordingWindow !== null && !this.recordingWindow.isDestroyed()
  }

  /**
   * 發送訊息到錄音視窗
   */
  sendToRecordingWindow(channel: string, data?: any) {
    if (this.isRecordingWindowValid()) {
      this.recordingWindow!.webContents.send(channel, data)
    }
  }

  /**
   * 清理所有視窗
   */
  cleanup() {
    this.closeRecordingWindow()
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close()
    }
  }
}
