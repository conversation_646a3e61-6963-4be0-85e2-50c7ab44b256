{"version": 3, "file": "WindowManager.js", "sourceRoot": "", "sources": ["../../../src/main/managers/WindowManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,uCAA6C;AAC7C,gDAAuB;AACvB,sDAA0E;AAE1E,MAAa,aAAa;IAA1B;QACU,eAAU,GAAyB,IAAI,CAAA;QACvC,oBAAe,GAAyB,IAAI,CAAA;IAgLtD,CAAC;IA9KC;;OAEG;IACH,gBAAgB;QACd,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,mBAAK,CAAC,CAAA;QAElD,IAAI,CAAC,UAAU,GAAG,IAAI,wBAAa,CAAC;YAClC,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,KAAK,EAAE,oBAAoB;YACjC,cAAc,EAAE;gBACd,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,KAAK;aACxB;YACD,IAAI,EAAE,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC;SACzD,CAAC,CAAA;QAEF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAEvC,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC,cAAG,CAAC,SAAS,EAAE,CAAC;gBACnB,KAAK,CAAC,cAAc,EAAE,CAAA;gBACtB,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACxB,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,mBAAK,CAAC,CAAA;QAEvD,IAAI,CAAC,eAAe,GAAG,IAAI,wBAAa,CAAC;YACvC,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE,KAAK,EAAE,gBAAgB;YAC7B,cAAc,EAAE;gBACd,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,KAAK;aACxB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAE5C,OAAO;QACP,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAA;QAE7B,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAqB;QAC7C,IAAI,mBAAK,EAAE,CAAC;YACV,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;YAC7C,MAAM,CAAC,OAAO,CAAC,IAAA,6BAAe,GAAE,CAAC,CAAA;YACjC,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,CAAA;YACnC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACvC,MAAM,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAA,yBAAW,GAAE,CAAC,CAAC,CAAA;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACzB,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAA;QACvB,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC9B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAA;QACtD,CAAC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE;gBAC5D,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,CAAA;gBAC5B,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,CAAA;YAC/B,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAA;IAC7E,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAAe,EAAE,IAAU;QAC/C,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;QACzB,CAAC;IACH,CAAC;CACF;AAlLD,sCAkLC"}