/**
 * Azure Speech Service 整合
 * 處理語音轉文字功能
 */

import * as sdk from 'microsoft-cognitiveservices-speech-sdk'

export interface SpeechConfig {
  subscriptionKey: string
  region: string
  language?: string
}

export interface SpeechResult {
  text: string
  confidence: number
  duration: number
}

export class AzureSpeechService {
  private speechConfig: sdk.SpeechConfig | null = null
  private audioConfig: sdk.AudioConfig | null = null

  constructor(config: SpeechConfig) {
    this.initializeConfig(config)
  }

  /**
   * 初始化 Speech Service 配置
   */
  private initializeConfig(config: SpeechConfig): void {
    try {
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        config.subscriptionKey,
        config.region
      )
      
      // 設定語言，預設為繁體中文
      this.speechConfig.speechRecognitionLanguage = config.language || 'zh-TW'
      
      // 設定輸出格式
      this.speechConfig.outputFormat = sdk.OutputFormat.Detailed
      
      console.log('Azure Speech Service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Azure Speech Service:', error)
      throw new Error('Azure Speech Service 初始化失敗')
    }
  }

  /**
   * 從音頻 Blob 進行語音識別
   */
  async recognizeFromBlob(audioBlob: Blob): Promise<SpeechResult> {
    if (!this.speechConfig) {
      throw new Error('Speech Service 未初始化')
    }

    return new Promise(async (resolve, reject) => {
      try {
        // 將 Blob 轉換為 ArrayBuffer
        const arrayBuffer = await this.blobToArrayBuffer(audioBlob)
        
        // 創建音頻配置
        const audioConfig = sdk.AudioConfig.fromWavFileInput(
          new Uint8Array(arrayBuffer)
        )
        
        // 創建語音識別器
        const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)
        
        const startTime = Date.now()
        
        // 設定事件處理器
        recognizer.recognizeOnceAsync(
          (result) => {
            const duration = Date.now() - startTime
            
            if (result.reason === sdk.ResultReason.RecognizedSpeech) {
              const speechResult: SpeechResult = {
                text: result.text,
                confidence: this.extractConfidence(result),
                duration
              }
              
              console.log('Speech recognition successful:', speechResult)
              resolve(speechResult)
            } else if (result.reason === sdk.ResultReason.NoMatch) {
              reject(new Error('無法識別語音內容，請重試'))
            } else {
              reject(new Error(`語音識別失敗: ${result.errorDetails}`))
            }
            
            recognizer.close()
          },
          (error) => {
            console.error('Speech recognition error:', error)
            recognizer.close()
            reject(new Error(`語音識別錯誤: ${error}`))
          }
        )
      } catch (error) {
        console.error('Error in recognizeFromBlob:', error)
        reject(error)
      }
    })
  }

  /**
   * 從音頻 Base64 字串進行語音識別
   */
  async recognizeFromBase64(audioBase64: string): Promise<SpeechResult> {
    try {
      // 將 Base64 轉換為 Blob
      const audioBlob = this.base64ToBlob(audioBase64)
      return await this.recognizeFromBlob(audioBlob)
    } catch (error) {
      console.error('Error in recognizeFromBase64:', error)
      throw error
    }
  }

  /**
   * 連續語音識別（用於即時轉錄）
   */
  async startContinuousRecognition(
    onResult: (text: string) => void,
    onError: (error: string) => void
  ): Promise<sdk.SpeechRecognizer> {
    if (!this.speechConfig) {
      throw new Error('Speech Service 未初始化')
    }

    // 使用預設麥克風
    const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput()
    const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)

    // 設定事件處理器
    recognizer.recognizing = (s, e) => {
      console.log(`Recognizing: ${e.result.text}`)
    }

    recognizer.recognized = (s, e) => {
      if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
        onResult(e.result.text)
      }
    }

    recognizer.canceled = (s, e) => {
      console.log(`Recognition canceled: ${e.reason}`)
      if (e.reason === sdk.CancellationReason.Error) {
        onError(`錯誤: ${e.errorDetails}`)
      }
      recognizer.stopContinuousRecognitionAsync()
    }

    recognizer.sessionStopped = (s, e) => {
      console.log('Recognition session stopped')
      recognizer.stopContinuousRecognitionAsync()
    }

    // 開始連續識別
    recognizer.startContinuousRecognitionAsync()
    
    return recognizer
  }

  /**
   * 停止連續語音識別
   */
  stopContinuousRecognition(recognizer: sdk.SpeechRecognizer): void {
    recognizer.stopContinuousRecognitionAsync(
      () => {
        recognizer.close()
        console.log('Continuous recognition stopped')
      },
      (error) => {
        console.error('Error stopping recognition:', error)
        recognizer.close()
      }
    )
  }

  /**
   * 從識別結果中提取信心度
   */
  private extractConfidence(result: sdk.SpeechRecognitionResult): number {
    try {
      // 嘗試從詳細結果中提取信心度
      const detailResult = JSON.parse(result.json)
      if (detailResult.NBest && detailResult.NBest.length > 0) {
        return detailResult.NBest[0].Confidence || 0.5
      }
    } catch (error) {
      console.warn('Could not extract confidence score:', error)
    }
    return 0.5 // 預設信心度
  }

  /**
   * 將 Blob 轉換為 ArrayBuffer
   */
  private async blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = reject
      reader.readAsArrayBuffer(blob)
    })
  }

  /**
   * 將 Base64 字串轉換為 Blob
   */
  private base64ToBlob(base64: string, mimeType: string = 'audio/webm'): Blob {
    const byteCharacters = atob(base64)
    const byteNumbers = new Array(byteCharacters.length)
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    
    const byteArray = new Uint8Array(byteNumbers)
    return new Blob([byteArray], { type: mimeType })
  }

  /**
   * 測試 Speech Service 連接
   */
  async testConnection(): Promise<boolean> {
    try {
      if (!this.speechConfig) {
        return false
      }

      // 創建一個簡單的測試識別器
      const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput()
      const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)
      
      // 測試是否能創建識別器
      recognizer.close()
      return true
    } catch (error) {
      console.error('Speech Service connection test failed:', error)
      return false
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: SpeechConfig): void {
    this.initializeConfig(config)
  }

  /**
   * 釋放資源
   */
  dispose(): void {
    this.speechConfig = null
    this.audioConfig = null
    console.log('Azure Speech Service disposed')
  }
}

export default AzureSpeechService
