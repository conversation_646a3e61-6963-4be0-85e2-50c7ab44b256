"use strict";
/**
 * IPC 管理器
 * 負責處理主程序和渲染程序之間的通信
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPCManager = void 0;
const electron_1 = require("electron");
const AudioProcessor_1 = require("../processors/AudioProcessor");
class IPCManager {
    constructor(windowManager, serviceManager) {
        this.windowManager = windowManager;
        this.serviceManager = serviceManager;
        this.audioProcessor = new AudioProcessor_1.AudioProcessor(serviceManager, windowManager);
    }
    /**
     * 設定所有 IPC 事件處理器
     */
    setupEventHandlers() {
        console.log('Setting up IPC event handlers...');
        // 處理快捷鍵更新
        electron_1.ipcMain.on('update-hotkeys', (event, newHotkeys) => {
            console.log('Received hotkey update:', newHotkeys);
            // 這裡需要通知 ShortcutManager 更新快捷鍵
            // 暫時先記錄，稍後實現
        });
        // 處理按鈕點擊開始錄音請求
        electron_1.ipcMain.on('start-recording-request', (event, data) => {
            const { mode } = data;
            console.log('Received start recording request from button click:', mode);
            // 這裡需要通知 RecordingController 開始錄音
            // 暫時先記錄，稍後實現
        });
        // 處理錄音完成事件
        electron_1.ipcMain.on('recording-completed', async (event, data) => {
            try {
                const { audioData, mode } = data;
                console.log('Received audio data for processing, mode:', mode);
                // 處理音頻數據
                await this.audioProcessor.processAudioData(audioData, mode);
            }
            catch (error) {
                console.error('Error in recording-completed handler:', error);
            }
        });
        // 處理錄音取消事件
        electron_1.ipcMain.on('recording-cancelled', (event) => {
            console.log('Recording cancelled by user');
            // 通知 RecordingController 取消錄音
            // 暫時先關閉錄音視窗
            this.windowManager.closeRecordingWindow();
        });
        // 處理重試錄音事件
        electron_1.ipcMain.on('retry-recording', (event) => {
            console.log('Retry recording requested by user');
            // 這裡需要通知 RecordingController 重試錄音
            // 暫時先記錄，稍後實現
        });
        // 處理 API 配置更新事件
        electron_1.ipcMain.on('update-api-config', async (event, apiConfig) => {
            try {
                await this.serviceManager.updateApiConfig(apiConfig);
                event.reply('api-config-updated', { success: true });
            }
            catch (error) {
                console.error('Failed to update API configuration:', error);
                event.reply('api-config-updated', {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        });
        console.log('IPC event handlers setup complete');
    }
    /**
     * 設定錄音控制器引用（用於處理錄音相關事件）
     */
    setRecordingController(recordingController) {
        // 更新事件處理器以使用錄音控制器
        electron_1.ipcMain.removeAllListeners('start-recording-request');
        electron_1.ipcMain.removeAllListeners('recording-cancelled');
        electron_1.ipcMain.removeAllListeners('retry-recording');
        // 重新設定事件處理器
        electron_1.ipcMain.on('start-recording-request', (event, data) => {
            const { mode } = data;
            console.log('Received start recording request from button click:', mode);
            recordingController.handleVoiceInput(mode);
        });
        electron_1.ipcMain.on('recording-cancelled', (event) => {
            console.log('Recording cancelled by user');
            recordingController.cancelRecording();
        });
        electron_1.ipcMain.on('retry-recording', (event) => {
            console.log('Retry recording requested by user');
            recordingController.retryRecording();
        });
    }
    /**
     * 設定快捷鍵管理器引用（用於處理快捷鍵更新）
     */
    setShortcutManager(shortcutManager) {
        electron_1.ipcMain.removeAllListeners('update-hotkeys');
        electron_1.ipcMain.on('update-hotkeys', (event, newHotkeys) => {
            console.log('Received hotkey update:', newHotkeys);
            shortcutManager.updateHotkeys(newHotkeys);
        });
    }
    /**
     * 清理所有 IPC 事件監聽器
     */
    cleanup() {
        electron_1.ipcMain.removeAllListeners('update-hotkeys');
        electron_1.ipcMain.removeAllListeners('start-recording-request');
        electron_1.ipcMain.removeAllListeners('recording-completed');
        electron_1.ipcMain.removeAllListeners('recording-cancelled');
        electron_1.ipcMain.removeAllListeners('retry-recording');
        electron_1.ipcMain.removeAllListeners('update-api-config');
    }
}
exports.IPCManager = IPCManager;
//# sourceMappingURL=IPCManager.js.map