import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Mic, Volume2, Square, Loader } from 'lucide-react'
import AudioRecorder from '../services/AudioRecorder'
import './RecordingPopup.css'

interface RecordingPopupProps {
  mode: 'ai' | 'direct'
  onClose: () => void
}

const RecordingPopup: React.FC<RecordingPopupProps> = ({ mode, onClose }) => {
  const [status, setStatus] = useState<'recording' | 'processing' | 'complete' | 'error'>('recording')
  const [duration, setDuration] = useState(0)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const audioRecorderRef = useRef<AudioRecorder | null>(null)

  useEffect(() => {
    let timer: NodeJS.Timeout

    const initializeRecording = async () => {
      try {
        // 創建音頻錄製器
        audioRecorderRef.current = new AudioRecorder()

        // 初始化並開始錄音
        await audioRecorderRef.current.initialize()
        await audioRecorderRef.current.startRecording()

        // 開始計時器
        timer = setInterval(() => {
          setDuration(prev => prev + 1)
        }, 1000)

        console.log('Recording started successfully')
      } catch (error) {
        console.error('Failed to start recording:', error)
        setStatus('error')
        setErrorMessage('無法開始錄音，請檢查麥克風權限')
      }
    }

    // 處理快捷鍵停止錄音
    const handleKeyPress = async (e: KeyboardEvent) => {
      if (e.key === 'Escape' || (e.ctrlKey && e.shiftKey && (e.key === 'C' || e.key === 'V'))) {
        await stopRecording()
      }
    }

    // 停止錄音並處理音頻
    const stopRecording = async () => {
      if (!audioRecorderRef.current || !audioRecorderRef.current.isRecording()) {
        return
      }

      try {
        setStatus('processing')

        // 停止錄音並獲取音頻數據
        const recordingData = await audioRecorderRef.current.stopRecording()

        // 將音頻轉換為 Base64 格式
        const audioBase64 = await AudioRecorder.blobToBase64(recordingData.audioBlob)

        // 發送音頻數據到主程序進行處理
        if (window.require) {
          const { ipcRenderer } = window.require('electron')
          ipcRenderer.send('recording-completed', {
            audioData: audioBase64,
            mode: mode,
            duration: recordingData.duration,
            sampleRate: recordingData.sampleRate
          })
        }

        console.log('Audio data sent for processing')
      } catch (error) {
        console.error('Failed to stop recording:', error)
        setStatus('error')
        setErrorMessage('錄音處理失敗，請重試')
      }
    }

    // 監聽來自主程序的訊息
    const handleIpcMessage = (event: any, data: any) => {
      console.log('RecordingPopup received IPC message:', event, data)
    }

    // 監聽處理完成訊息
    const handleProcessingComplete = (event: any, data: any) => {
      console.log('Processing complete:', data)
      setStatus('complete')
      // 不要立即關閉，讓主程序控制關閉時機
    }

    // 監聽處理錯誤訊息
    const handleProcessingError = (event: any, data: any) => {
      console.log('Processing error:', data)
      setStatus('error')
      setErrorMessage(data.message || '處理失敗，請重試')
    }

    // 監聽停止錄音訊息
    const handleStopRecording = async () => {
      console.log('Received stop recording signal from main process')
      await stopRecording()
    }

    // 初始化錄音
    initializeRecording()

    // 添加事件監聽器
    window.addEventListener('keydown', handleKeyPress)

    if (window.require) {
      const { ipcRenderer } = window.require('electron')
      ipcRenderer.on('processing-complete', handleProcessingComplete)
      ipcRenderer.on('processing-error', handleProcessingError)
      ipcRenderer.on('stop-recording', handleStopRecording)
    }

    return () => {
      // 清理資源
      if (timer) {
        clearInterval(timer)
      }

      if (audioRecorderRef.current) {
        audioRecorderRef.current.dispose()
      }

      window.removeEventListener('keydown', handleKeyPress)

      if (window.require) {
        const { ipcRenderer } = window.require('electron')
        ipcRenderer.removeAllListeners('processing-complete')
        ipcRenderer.removeAllListeners('processing-error')
        ipcRenderer.removeAllListeners('stop-recording')
      }
    }
  }, [mode, onClose])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusText = () => {
    switch (status) {
      case 'recording':
        return mode === 'ai' ? '正在錄音 - AI 模式' : '正在錄音 - 直接模式'
      case 'processing':
        return mode === 'ai' ? 'AI 正在處理...' : '語音轉換中...'
      case 'complete':
        return '完成！'
      case 'error':
        return '錯誤'
    }
  }

  const getIcon = () => {
    switch (status) {
      case 'recording':
        return mode === 'ai' ? <Mic size={32} /> : <Volume2 size={32} />
      case 'processing':
        return <Loader size={32} className="spinning" />
      case 'complete':
        return <Square size={32} />
      case 'error':
        return <Square size={32} />
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        className="recording-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className={`recording-popup ${mode}-mode`}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <div className="recording-content">
            <motion.div
              className="recording-icon"
              animate={status === 'recording' ? { scale: [1, 1.1, 1] } : {}}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              {getIcon()}
            </motion.div>

            <h3>{getStatusText()}</h3>

            {status === 'recording' && (
              <div className="recording-info">
                <div className="duration">{formatDuration(duration)}</div>
                <div className="hint">再次按快捷鍵停止錄音</div>
              </div>
            )}

            {status === 'processing' && (
              <div className="processing-info">
                <motion.div
                  className="progress-bar"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2 }}
                />
              </div>
            )}

            {status === 'complete' && (
              <motion.div
                className="complete-info"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <p>文字已自動輸入到應用程式</p>
              </motion.div>
            )}

            {status === 'error' && (
              <motion.div
                className="error-info"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <p>{errorMessage}</p>
                <button
                  className="retry-btn"
                  onClick={() => {
                    // 發送重試請求到主程序
                    if (window.require) {
                      const { ipcRenderer } = window.require('electron')
                      ipcRenderer.send('retry-recording')
                    } else {
                      // 如果沒有 electron 環境，直接重置狀態
                      setStatus('recording')
                      setErrorMessage('')
                      setDuration(0)
                    }
                  }}
                >
                  重試
                </button>
              </motion.div>
            )}
          </div>

          {status === 'recording' && (
            <motion.button
              className="stop-btn"
              onClick={async () => {
                if (audioRecorderRef.current && audioRecorderRef.current.isRecording()) {
                  try {
                    setStatus('processing')

                    const recordingData = await audioRecorderRef.current.stopRecording()
                    const audioBase64 = await AudioRecorder.blobToBase64(recordingData.audioBlob)

                    if (window.require) {
                      const { ipcRenderer } = window.require('electron')
                      ipcRenderer.send('recording-completed', {
                        audioData: audioBase64,
                        mode: mode,
                        duration: recordingData.duration,
                        sampleRate: recordingData.sampleRate
                      })
                    }
                  } catch (error) {
                    console.error('Failed to stop recording:', error)
                    setStatus('error')
                    setErrorMessage('停止錄音失敗，請重試')
                  }
                }
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Square size={16} />
              停止錄音
            </motion.button>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default RecordingPopup
