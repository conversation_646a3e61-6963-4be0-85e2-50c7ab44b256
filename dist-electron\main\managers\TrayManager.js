"use strict";
/**
 * 托盤管理器
 * 負責創建和管理系統托盤圖示
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrayManager = void 0;
const electron_1 = require("electron");
const path_1 = __importDefault(require("path"));
class TrayManager {
    constructor(windowManager) {
        this.tray = null;
        this.windowManager = windowManager;
    }
    /**
     * 創建系統托盤
     */
    createTray() {
        const trayIconPath = path_1.default.join(__dirname, '../../assets/tray-icon.png');
        console.log('Tray icon path:', trayIconPath);
        this.tray = new electron_1.Tray(trayIconPath);
        this.tray.setToolTip('SpeechPilot - 語音助手');
        // 設定托盤選單
        this.updateTrayMenu();
        // 點擊托盤圖示顯示主視窗
        this.tray.on('click', () => {
            this.windowManager.showMainWindow();
        });
        // 雙擊托盤圖示顯示主視窗
        this.tray.on('double-click', () => {
            this.windowManager.showMainWindow();
        });
    }
    /**
     * 更新托盤選單
     */
    updateTrayMenu() {
        if (!this.tray)
            return;
        const contextMenu = electron_1.Menu.buildFromTemplate([
            {
                label: '顯示主視窗',
                click: () => {
                    this.windowManager.showMainWindow();
                }
            },
            {
                type: 'separator'
            },
            {
                label: 'AI 語音助手 (Ctrl+Shift+C)',
                click: () => {
                    // 這裡可以觸發 AI 模式錄音
                    console.log('AI mode triggered from tray');
                }
            },
            {
                label: '直接語音轉文字 (Ctrl+Shift+V)',
                click: () => {
                    // 這裡可以觸發直接模式錄音
                    console.log('Direct mode triggered from tray');
                }
            },
            {
                type: 'separator'
            },
            {
                label: '設定',
                click: () => {
                    this.windowManager.showMainWindow();
                }
            },
            {
                type: 'separator'
            },
            {
                label: '退出',
                click: () => {
                    electron_1.app.isQuiting = true;
                    electron_1.app.quit();
                }
            }
        ]);
        this.tray.setContextMenu(contextMenu);
    }
    /**
     * 銷毀托盤
     */
    destroy() {
        if (this.tray) {
            this.tray.destroy();
            this.tray = null;
        }
    }
    /**
     * 獲取托盤實例
     */
    getTray() {
        return this.tray;
    }
}
exports.TrayManager = TrayManager;
//# sourceMappingURL=TrayManager.js.map