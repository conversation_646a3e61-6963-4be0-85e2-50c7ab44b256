{"version": 3, "file": "IPCManager.js", "sourceRoot": "", "sources": ["../../../src/main/managers/IPCManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,uCAAkC;AAGlC,iEAA6D;AAE7D,MAAa,UAAU;IAKrB,YAAY,aAA4B,EAAE,cAA8B;QACtE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;QAClC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,cAAc,EAAE,aAAa,CAAC,CAAA;IACzE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;QAE/C,UAAU;QACV,kBAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAA;YAClD,+BAA+B;YAC/B,aAAa;QACf,CAAC,CAAC,CAAA;QAEF,eAAe;QACf,kBAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACpD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAA;YACrB,OAAO,CAAC,GAAG,CAAC,qDAAqD,EAAE,IAAI,CAAC,CAAA;YACxE,kCAAkC;YAClC,aAAa;QACf,CAAC,CAAC,CAAA;QAEF,WAAW;QACX,kBAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAA;gBAChC,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC,CAAA;gBAE9D,SAAS;gBACT,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;YAC7D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;YAC/D,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,WAAW;QACX,kBAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;YAC1C,8BAA8B;YAC9B,YAAY;YACZ,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;QAC3C,CAAC,CAAC,CAAA;QAEF,WAAW;QACX,kBAAO,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,kCAAkC;YAClC,aAAa;QACf,CAAC,CAAC,CAAA;QAEF,gBAAgB;QAChB,kBAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE;YACzD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;gBACpD,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;gBAC3D,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBAChC,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,mBAAwB;QAC7C,kBAAkB;QAClB,kBAAO,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAA;QACrD,kBAAO,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAA;QACjD,kBAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAA;QAE7C,YAAY;QACZ,kBAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACpD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAA;YACrB,OAAO,CAAC,GAAG,CAAC,qDAAqD,EAAE,IAAI,CAAC,CAAA;YACxE,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAC5C,CAAC,CAAC,CAAA;QAEF,kBAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;YAC1C,mBAAmB,CAAC,eAAe,EAAE,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,kBAAO,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,mBAAmB,CAAC,cAAc,EAAE,CAAA;QACtC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,eAAoB;QACrC,kBAAO,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAA;QAE5C,kBAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAA;YAClD,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,kBAAO,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAA;QAC5C,kBAAO,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAA;QACrD,kBAAO,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAA;QACjD,kBAAO,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAA;QACjD,kBAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAA;QAC7C,kBAAO,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAA;IACjD,CAAC;CACF;AA/HD,gCA+HC"}