/**
 * 托盤管理器
 * 負責創建和管理系統托盤圖示
 */

import { Tray, Menu, app } from 'electron'
import path from 'path'
import { WindowManager } from './WindowManager'

export class TrayManager {
  private tray: Tray | null = null
  private windowManager: WindowManager

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager
  }

  /**
   * 創建系統托盤
   */
  createTray() {
    const trayIconPath = path.join(__dirname, '../../assets/tray-icon.png')
    console.log('Tray icon path:', trayIconPath)

    this.tray = new Tray(trayIconPath)
    this.tray.setToolTip('SpeechPilot - 語音助手')

    // 設定托盤選單
    this.updateTrayMenu()

    // 點擊托盤圖示顯示主視窗
    this.tray.on('click', () => {
      this.windowManager.showMainWindow()
    })

    // 雙擊托盤圖示顯示主視窗
    this.tray.on('double-click', () => {
      this.windowManager.showMainWindow()
    })
  }

  /**
   * 更新托盤選單
   */
  updateTrayMenu() {
    if (!this.tray) return

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '顯示主視窗',
        click: () => {
          this.windowManager.showMainWindow()
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'AI 語音助手 (Ctrl+Shift+C)',
        click: () => {
          // 這裡可以觸發 AI 模式錄音
          console.log('AI mode triggered from tray')
        }
      },
      {
        label: '直接語音轉文字 (Ctrl+Shift+V)',
        click: () => {
          // 這裡可以觸發直接模式錄音
          console.log('Direct mode triggered from tray')
        }
      },
      {
        type: 'separator'
      },
      {
        label: '設定',
        click: () => {
          this.windowManager.showMainWindow()
        }
      },
      {
        type: 'separator'
      },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    this.tray.setContextMenu(contextMenu)
  }

  /**
   * 銷毀托盤
   */
  destroy() {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }
  }

  /**
   * 獲取托盤實例
   */
  getTray(): Tray | null {
    return this.tray
  }
}
