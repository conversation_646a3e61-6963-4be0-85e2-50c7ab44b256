.recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.recording-popup {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 300px;
  max-width: 400px;
}

.recording-popup.ai-mode {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-color: rgba(102, 126, 234, 0.3);
}

.recording-popup.direct-mode {
  background: linear-gradient(135deg, rgba(34, 193, 195, 0.2), rgba(253, 187, 45, 0.2));
  border-color: rgba(34, 193, 195, 0.3);
}

.recording-content {
  margin-bottom: 1.5rem;
}

.recording-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 auto 1rem auto;
}

.recording-popup h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.recording-info {
  margin-bottom: 1rem;
}

.duration {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-family: 'Courier New', monospace;
}

.hint {
  font-size: 0.9rem;
  opacity: 0.8;
}

.processing-info {
  margin: 1rem 0;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  margin: 1rem 0;
}

.complete-info p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.error-info {
  margin: 1rem 0;
  text-align: center;
}

.error-info p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #ff6b6b;
  opacity: 0.9;
}

.retry-btn {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.4);
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 107, 107, 0.5);
}

.stop-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 0.8rem 1.5rem;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.stop-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
