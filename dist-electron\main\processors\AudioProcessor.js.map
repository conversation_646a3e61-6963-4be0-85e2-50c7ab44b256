{"version": 3, "file": "AudioProcessor.js", "sourceRoot": "", "sources": ["../../../src/main/processors/AudioProcessor.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,uCAAoC;AAMpC,MAAa,cAAc;IAIzB,YAAY,cAA8B,EAAE,aAA4B;QACtE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,IAAoB;QAC5D,IAAI,CAAC;YACH,IAAI,UAAU,GAAG,EAAE,CAAA;YAEnB,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,0BAA0B;gBAC1B,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YAClD,CAAC;iBAAM,CAAC;gBACN,iCAAiC;gBACjC,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;YACtD,CAAC;YAED,gBAAgB;YAChB,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;gBAC9D,IAAI,EAAE,UAAU;aACjB,CAAC,CAAA;YAEF,kBAAkB;YAClB,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAE/B,qBAAqB;YACrB,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;YAC3C,CAAC,EAAE,IAAI,CAAC,CAAA;QAEV,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YAEpD,cAAc;YACd,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,kBAAkB,EAAE;gBAC3D,OAAO,EAAE,eAAe;aACzB,CAAC,CAAA;YAEF,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAA;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA;QAE1D,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACjC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,KAAK,CAAA;YACb,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,OAAO,GAAG,4BAA4B,CAAA;gBAC5C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACrB,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC7B,OAAO,kBAAkB,CAAA;YAC3B,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,wDAAwD,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA;YACvF,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAEhC,iCAAiC;YACjC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;YAEjE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;gBACzC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,KAAK,CAAA;YACb,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;YACtD,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAC9B,OAAO,MAAM,CAAC,IAAI,CAAA;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACnD,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;YACrC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,SAAiB;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAA;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAA;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA;QAE1D,IAAI,CAAC;YACH,WAAW;YACX,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;YAE1D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,OAAO,GAAG,8BAA8B,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACrB,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC7B,OAAO,UAAU,CAAA;YACnB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YACzF,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YAElC,uBAAuB;YACvB,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;YAEnE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;gBACxC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,KAAK,CAAA;YACb,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAC3E,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;YACjC,OAAO,UAAU,CAAA;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC/C,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;YACrC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,QAAQ,CAAC,IAAY;QACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAA;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA;QAE1D,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;gBAC/B,OAAM;YACR,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAA;gBACtE,oBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACzB,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC9B,OAAM;YACR,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;YACnF,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAEhC,6BAA6B;YAC7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;YAEtD,WAAW;YACX,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAE1D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;gBACpC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAChC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAA;gBAC7D,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;gBAEtC,OAAO;gBACP,MAAM,iBAAiB,GAAG,oBAAS,CAAC,QAAQ,EAAE,CAAA;gBAC9C,oBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACzB,UAAU,CAAC,GAAG,EAAE;oBACd,oBAAS,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;gBACxC,CAAC,EAAE,IAAI,CAAC,CAAA;YACV,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;YAExC,UAAU;YACV,IAAI,CAAC;gBACH,MAAM,iBAAiB,GAAG,oBAAS,CAAC,QAAQ,EAAE,CAAA;gBAC9C,oBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;gBACpC,UAAU,CAAC,GAAG,EAAE;oBACd,oBAAS,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;gBACxC,CAAC,EAAE,IAAI,CAAC,CAAA;YACV,CAAC;YAAC,OAAO,cAAc,EAAE,CAAC;gBACxB,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACtC,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAlMD,wCAkMC"}